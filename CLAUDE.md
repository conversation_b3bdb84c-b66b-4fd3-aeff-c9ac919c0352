# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This repository contains Model Context Protocol (MCP) servers for different APIs and services:

- **binance-mcp-server**: TypeScript-based MCP server for Binance cryptocurrency exchange API
- **weather**: Python-based MCP server for National Weather Service (NWS) API
- **coinex-mcp-server**: (Empty directory, appears to be planned)

## Development Commands

### Binance MCP Server (TypeScript)
- **Run the server**: `node binance-mcp-server/index.js`
- **Development**: No package.json found - likely needs Node.js dependencies installed manually
- **Environment**: Requires `BINANCE_API_KEY`, `BINANCE_API_SECRET`, optional `BINANCE_TESTNET=true`

### Weather MCP Server (Python)
- **Run the server**: `cd weather && python weather.py`
- **Development**: Uses `uv` for dependency management
- **Install dependencies**: `cd weather && uv sync`
- **Environment**: No environment variables required (uses public NWS API)

## Architecture

### Binance MCP Server Structure
- **Entry point**: `index.ts` - initializes and starts the server
- **Core server**: `server.ts` - implements MCP protocol with Binance API integration
- **Tools**: Organized into categories in `tools/` directory:
  - `market-data.ts` - Price data, order books, klines, 24hr tickers
  - `account.ts` - Account information, balances
  - `trading.ts` - Order placement, cancellation, history
- **Configuration**: `config/binance.ts` - environment validation and API configuration
- **Types**: Comprehensive TypeScript definitions for Binance API responses and MCP schemas
- **Error handling**: Centralized error handling with sanitization

### Weather MCP Server Structure
- **Entry point**: `weather.py` - FastMCP-based server
- **Tools**: Two main functions:
  - `get_alerts(state)` - Weather alerts by US state
  - `get_forecast(latitude, longitude)` - Weather forecast by coordinates
- **API**: Uses National Weather Service API with proper User-Agent headers

### Common MCP Patterns
Both servers follow the MCP (Model Context Protocol) standard:
- Tool registration with name, description, and JSON schema
- Request handlers for `ListTools` and `CallTool`
- Structured error responses with sanitized messages
- JSON-formatted tool outputs

## Environment Configuration

### Required for Binance Server
```bash
BINANCE_API_KEY=your_api_key
BINANCE_API_SECRET=your_api_secret
BINANCE_TESTNET=true  # Optional, for testnet usage
```

### Optional Configuration
```bash
MCP_SERVER_NAME=binance-mcp-server  # Default server name
MCP_SERVER_VERSION=1.0.0           # Default version
LOG_LEVEL=info                     # Logging level
```

## Security Considerations

- API credentials are loaded from environment variables only
- Error messages are sanitized to prevent sensitive data leakage
- Binance server supports both testnet and mainnet environments
- Weather server uses public API with no authentication required

## Output Format
- 总是用中文回复