#!/usr/bin/env python3
"""
CoinEx客户端单元测试
测试coinex_client.py中的所有功能
"""

import os
from decimal import Decimal
from time import sleep

import pytest
import httpx
from unittest.mock import Mock, patch, AsyncMock
from coinex_client import CoinExClient, validate_environment, get_coinex_client


class TestCoinExClient:
    """CoinExClient类的测试"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.client = CoinExClient()

    def test_generate_signature(self):
        """测试签名生成"""
        method = "GET" 
        path = "/v2/spot/ticker"
        params = {"market": "BTCUSDT"}
        
        signature, timestamp = self.client._generate_signature(method, path, params)
        
        assert signature is not None
        assert len(signature) == 64  # SHA256 hex字符串长度
        assert timestamp.isdigit()
        assert len(timestamp) == 13  # 毫秒时间戳长度
    
    def test_get_headers_with_auth(self):
        """测试获取需要认证的请求头"""
        headers = self.client._get_headers("GET", "/v2/assets/spot/balance")
        
        assert "Content-Type" in headers
        assert "User-Agent" in headers
        assert "X-COINEX-KEY" in headers
        assert "X-COINEX-SIGN" in headers
        assert "X-COINEX-TIMESTAMP" in headers

    @staticmethod
    def _asset_call_success(result):
        assert isinstance(result, dict)
        assert result["code"] == 0
        assert result["message"] == "OK"

    @classmethod
    def _asset_ticker_result(cls, result):
        data = result["data"]
        assert isinstance(data, list)
        for item in data:
            assert isinstance(item, dict)
            cls._asset_price_item(item)
            assert 'period' in item
            assert 'volume_buy' in item
            assert 'volume_sell' in item

    @staticmethod
    def _asset_price_item(item):
        assert "close" in item
        assert "open" in item
        assert "high" in item
        assert "low" in item
        assert "volume" in item
        assert "value" in item

    @pytest.mark.asyncio
    async def test_get_ticker(self):
        """测试GET请求成功"""
        result = await self.client.get_ticker("BTCUSDT,ETHUSDT")

        self._asset_call_success(result)
        self._asset_ticker_result(result)

    @pytest.mark.asyncio
    async def test_get_ticker_all_markets(self):
        """测试获取所有市场行情"""
        result = await self.client.get_ticker()
        self._asset_call_success(result)
        self._asset_ticker_result(result)
    
    @pytest.mark.asyncio
    async def test_get_depth(self):
        """测试获取市场深度"""
        market = 'BTCUSDT'

        result = await self.client.get_depth(market, limit=10, interval="0.1")

        self._asset_call_success(result)
        data = result["data"]
        assert isinstance(data, dict)
        assert data.get('market', '') == market
        depth = data.get('depth', None)
        assert isinstance(depth, dict)
        asks = depth.get('asks', None)
        assert isinstance(asks, list)
        assert isinstance(asks[0], list)
        bids = depth.get('bids', None)
        assert isinstance(bids, list)
        assert isinstance(bids[0], list)

    @pytest.mark.asyncio
    async def test_get_klines(self):
        """测试获取K线数据"""
        market = 'BTCUSDT'
        result = await self.client.get_klines(market, "1hour", limit=50)
            
        self._asset_call_success(result)
        data = result["data"]
        assert isinstance(data, list)
        assert data[0].get('market', '') == market
        self._asset_price_item(data[0])

    @pytest.mark.asyncio
    async def test_get_balances(self):
        """测试获取账户余额成功"""
        result = await self.client.get_balances()

        self._asset_call_success(result)
        data = result["data"]
        assert isinstance(data, list)
        if len(data) > 0:
            assert 'ccy' in data[0]
            assert 'available' in data[0]
            assert 'frozen' in data[0]

    @pytest.mark.asyncio
    async def test_get_order_history(self):
        """测试获取订单历史成功"""
        result = await self.client.get_order_history(market="BTCUSDT", limit=50)
        print(result)
        self._asset_call_success(result)
        data = result["data"]
        assert isinstance(data, list)

    @pytest.mark.asyncio
    async def test_get_order_history_all_markets(self):
        """测试获取所有市场的订单历史"""
        mock_response_data = {"code": 0, "data": {"records": []}}
        
        with patch.object(self.client, '_request', return_value=mock_response_data) as mock_request:
            result = await self.client.get_order_history(limit=100)
            
            expected_params = {"limit": 100}
            mock_request.assert_called_once_with("GET", "/v1/order/finished", expected_params)
            assert result == mock_response_data

    @pytest.mark.asyncio
    async def test_get_open_orders_success(self):
        """测试获取当前挂单成功"""
        mock_response_data = {"code": 0, "data": {"orders": []}}
        
        with patch.object(self.client, '_request', return_value=mock_response_data) as mock_request:
            result = await self.client.get_open_orders("BTCUSDT")
            
            expected_params = {"market": "BTCUSDT"}
            mock_request.assert_called_once_with("GET", "/v1/order/pending", expected_params)
            assert result == mock_response_data
    
    @pytest.mark.asyncio
    async def test_get_open_orders_all_markets(self):
        """测试获取所有市场的当前挂单"""
        mock_response_data = {"code": 0, "data": {"orders": []}}
        
        with patch.object(self.client, '_request', return_value=mock_response_data) as mock_request:
            result = await self.client.get_open_orders()
            
            expected_params = {}
            mock_request.assert_called_once_with("GET", "/v1/order/pending", expected_params)
            assert result == mock_response_data

    @pytest.mark.asyncio
    async def test_place_order(self):
        """测试市价单下单"""
        market = 'USDCUSDT'
        market_type = 'SPOT'

        price_res = await self.client.get_ticker(market)
        assert price_res["code"] == 0
        price = Decimal(price_res['data'][0]['close'])

        result = await self.client.place_order(
            market=market, market_type=market_type, type="limit", side="buy", amount="1.5",
            price=str(price*Decimal(0.8))
        )
        print(result)
        assert isinstance(result, dict)
        assert result["code"] == 0
        assert result["message"] == "OK"
        sleep(1)

        result = await self.client.cancel_order(market=market, market_type=market_type, order_id=result["data"]["order_id"])
        assert result["code"] == 0
        assert result["message"] == "OK"


class TestUtilityFunctions:
    """测试工具函数"""
    
    def test_validate_environment_with_credentials(self):
        """测试环境验证-有凭证"""
        with patch.dict(os.environ, {
            'COINEX_ACCESS_ID': 'test_access_id',
            'COINEX_SECRET_KEY': 'test_secret_key'
        }):
            result = validate_environment()
            assert result == True
    
    def test_validate_environment_without_credentials(self):
        """测试环境验证-无凭证"""
        with patch.dict(os.environ, {}, clear=True):
            with patch('builtins.print') as mock_print:
                result = validate_environment()
                assert result == False
                mock_print.assert_called()
    
    def test_get_coinex_client(self):
        """测试获取客户端实例"""
        client = get_coinex_client()
        assert isinstance(client, CoinExClient)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])