# CoinEx MCP Server

基于Python的CoinEx加密货币交易所MCP服务器，提供市场数据、账户信息和交易功能。

## 功能特性

### 市场数据工具 (无需认证)
- `get_ticker` - 获取价格行情信息
- `get_orderbook` - 获取订单簿深度数据  
- `get_kline` - 获取K线历史数据

### 账户管理工具 (需要API凭证)
- `get_account_info` - 获取账户余额信息

### 交易工具 (需要API凭证)
- `place_order` - 下单交易
- `cancel_order` - 取消订单
- `get_order_history` - 获取订单历史

## 安装和运行

### 1. 配置API凭证 (可选)
在CoinEx官网申请API凭证：
1. 登录 [CoinEx](https://www.coinex.com/)
2. 进入 "账户" -> "API管理"
3. 创建API凭证，获取 Access ID 和Secret Key

复制环境变量配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入您的API凭证：
```bash
COINEX_ACCESS_ID=your_access_id_here
COINEX_SECRET_KEY=your_secret_key_here
COINEX_TESTNET=false
```

### 2. 安装依赖
```bash
cd coinex-mcp-server
uv sync
```

### 3. 运行服务器
```bash
python main.py
```

### 4. 测试服务器
使用MCP客户端连接测试功能：

**市场数据测试**：
```bash
# 获取所有交易对行情
get_ticker

# 获取特定交易对行情  
get_ticker --symbol BTCUSDT

# 获取订单簿
get_orderbook --symbol BTCUSDT --limit 10

# 获取K线数据
get_kline --symbol BTCUSDT --period 1hour --limit 50
```

**账户和交易测试** (需要API凭证)：
```bash
# 获取账户信息
get_account_info

# 下限价单
place_order --symbol BTCUSDT --side buy --type limit --quantity 0.001 --price 40000

# 查看订单历史
get_order_history --symbol BTCUSDT --limit 10
```

## 使用模式

### 真实API模式
当配置了API凭证时，服务器将使用真实的CoinEx API：
- 市场数据工具无需认证，直接调用公开API
- 账户和交易工具需要API凭证的签名认证
- 返回真实的市场数据和交易信息

### Mock模式
当没有配置API凭证时，服务器会自动进入Mock模式：
- 所有功能都使用模拟数据
- 模拟价格数据（基于真实价格范围的随机波动）
- 模拟订单簿、账户余额和交易订单
- 支持的模拟交易对：BTCUSDT, ETHUSDT, ADAUSDT, DOGEUSDT

## API参考

### 环境变量
- `COINEX_ACCESS_ID`: CoinEx API Access ID
- `COINEX_SECRET_KEY`: CoinEx API Secret Key  
- `COINEX_TESTNET`: 是否使用测试网 (true/false)

### 认证机制
服务器使用HMAC-SHA256签名认证，符合CoinEx API v2规范。

### 错误处理
- 网络超时和连接错误处理
- API错误响应解析和友好错误信息
- 缺少凭证时的友好提示

## 项目结构

```
coinex-mcp-server/
├── main.py           # 主服务器文件，包含所有MCP工具
├── coinex_client.py  # CoinEx API客户端实现
├── pyproject.toml    # Python项目配置
├── .env.example      # 环境变量配置示例
├── README.md         # 项目文档
└── tools/            # 工具模块目录（为将来扩展预留）
```

## 注意事项

1. **安全性**: API凭证仅从环境变量中读取，不会记录在日志中
2. **权限管理**: 建议为API凭证设置最小必要权限
3. **网络环境**: 确保服务器可以访问 CoinEx API (api.coinex.com)
4. **速率限制**: 请遵守CoinEx API的速率限制规定

## 常见问题

**Q: 为什么返回Mock数据？**
A: 检查是否正确配置了 `COINEX_ACCESS_ID` 和 `COINEX_SECRET_KEY` 环境变量。

**Q: 接口返回"需要access_id和secret_key"错误？**
A: 说明您在调用需要认证的接口，但没有配置或配置不正确的API凭证。

**Q: 如何切换到测试环境？**
A: 设置环境变量 `COINEX_TESTNET=true`。注意：CoinEx可能没有独立的测试网。