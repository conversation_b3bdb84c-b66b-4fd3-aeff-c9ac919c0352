#!/usr/bin/env python3
"""
调试订单簿问题
"""

import asyncio
import json
import os
from coinex_client import CoinExClient


async def debug_orderbook():
    """调试订单簿问题"""
    
    os.environ['COINEX_ACCESS_ID'] = 'test'
    os.environ['COINEX_SECRET_KEY'] = 'test'
    
    client = CoinExClient()
    
    print("调试订单簿API...")
    
    try:
        result = await client.get_depth("BTCUSDT", 3, "0")
        print("原始API响应:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"API调用错误: {e}")
    
    # 测试MCP工具
    print("\n测试MCP订单簿工具:")
    
    import importlib
    import main
    importlib.reload(main)
    
    from main import get_orderbook
    
    try:
        result = await get_orderbook("BTCUSDT", 3)
        print("MCP工具结果:")
        print(result)
    except Exception as e:
        print(f"MCP工具错误: {e}")


if __name__ == "__main__":
    asyncio.run(debug_orderbook())