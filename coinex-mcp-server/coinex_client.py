"""
CoinEx API客户端
实现CoinEx API v2的认证、签名和请求功能
"""

import os
import time
import hmac
import hashlib
import json
from typing import Any, Dict, Optional, List
from urllib.parse import urlencode
import httpx
import logging


class CoinExClient:
    """CoinEx API客户端"""
    
    def __init__(self, access_id: str = None, secret_key: str = None):
        """初始化CoinEx客户端
        :param access_id: API访问ID
        :param secret_key: API密钥
        """

        self.access_id = access_id or os.getenv('COINEX_ACCESS_ID')
        self.secret_key = secret_key or os.getenv('COINEX_SECRET_KEY')
        logging.info(f"self.access_id: {self.access_id}")
        logging.info(f"self.secret_key: {self.secret_key}")
        self.base_url = "https://api.coinex.com"  # CoinEx可能没有专门的测试网，使用主网
        self.timeout = 30
        
    def _generate_signature(self, method: str, path: str, params: Dict = None, body: str = "") -> tuple[str, str]:
        """生成API签名"""
        if not self.secret_key:
            raise ValueError("需要secret_key来生成签名")
            
        timestamp = str(int(time.time() * 1000))
        
        # 构建待签名字符串
        if params:
            query_string = urlencode(sorted(params.items()))
            prepared_str = f"{method}{path}?{query_string}{body}{timestamp}"
        else:
            prepared_str = f"{method}{path}{body}{timestamp}"
        
        # 使用HMAC-SHA256生成签名
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            prepared_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature, timestamp
    
    def _get_headers(self, method: str, path: str, params: Dict = None, body: str = "") -> Dict[str, str]:
        """获取请求头"""
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "coinex-mcp-server/1.0"
        }
        
        # 如果有认证信息，添加签名头
        if self.access_id and self.secret_key:
            signature, timestamp = self._generate_signature(method, path, params, body)
            headers.update({
                "X-COINEX-KEY": self.access_id,
                "X-COINEX-SIGN": signature,
                "X-COINEX-TIMESTAMP": timestamp
            })
        
        return headers
    
    async def _request(self, method: str, path: str, params: Dict = None, body: Dict = None) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{path}"
        
        # 准备请求体
        request_body = ""
        if body:
            request_body = json.dumps(body, separators=(',', ':'))
        
        # 获取请求头
        headers = self._get_headers(method, path, params, request_body)
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            print(f"请求URL: {url}")
            print(f"请求方法: {method}")
            print(f"请求参数: {params}")
            print(f"请求头: {headers}")
            try:
                if method.upper() == "GET":
                    response = await client.get(url, params=params, headers=headers)
                elif method.upper() == "POST":
                    response = await client.post(url, params=params, headers=headers, content=request_body)
                elif method.upper() == "DELETE":
                    if request_body:
                        response = await client.request("DELETE", url, params=params, headers=headers, content=request_body)
                    else:
                        response = await client.delete(url, params=params, headers=headers)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                
                response.raise_for_status()
                return response.json()
                
            except httpx.TimeoutException:
                raise Exception("请求超时")
            except httpx.HTTPStatusError as e:
                error_msg = f"HTTP错误 {e.response.status_code}"
                try:
                    error_data = e.response.json()
                    if 'message' in error_data:
                        error_msg += f": {error_data['message']}"
                except:
                    pass
                raise Exception(error_msg)
            except Exception as e:
                raise Exception(f"请求失败: {str(e)}")
    
    # 市场数据接口 (无需认证)
    async def get_ticker(self, market: str = None) -> Dict[str, Any]:
        """获取市场行情"""
        if market:
            path = f"/v2/spot/ticker"
            params = {"market": market}
        else:
            path = "/v2/spot/ticker"
            params = {}
        
        return await self._request("GET", path, params)
    
    async def get_depth(self, market: str, limit: int = 20, interval: str = "0") -> Dict[str, Any]:
        """获取市场深度"""
        path = "/v2/spot/depth"
        params = {
            "market": market,
            "limit": limit,
            "interval": interval  # CoinEx API需要这个参数
        }
        
        return await self._request("GET", path, params)
    
    async def get_klines(self, market: str, period: str, limit: int = 100) -> Dict[str, Any]:
        """获取K线数据"""
        path = "/v2/spot/kline"
        params = {
            "market": market,
            "period": period,
            "limit": limit
        }
        
        return await self._request("GET", path, params)
    
    # 账户接口 (需要认证)
    async def get_balances(self) -> Dict[str, Any]:
        """获取账户余额"""
        if not self.access_id or not self.secret_key:
            raise ValueError("账户接口需要access_id和secret_key")
        
        # 使用正确的CoinEx API端点
        path = "/v2/assets/spot/balance"
        return await self._request("GET", path)
    
    # 交易接口 (需要认证)
    async def place_order(self, market: str, market_type: str, type: str, side: str, amount: str,
                         price: str = None, client_id: str = None) -> Dict[str, Any]:
        """下单"""
        if not self.access_id or not self.secret_key:
            raise ValueError("交易接口需要access_id和secret_key")
        
        path = "/v2/spot/order"
        body = {
            "market": market,
            "market_type": market_type,
            "type": type,
            "side": side,
            "amount": amount,
        }
        
        if price:
            body["price"] = price
        if client_id:
            body["client_id"] = client_id
        
        return await self._request("POST", path, body=body)
    
    async def cancel_order(self, market: str, market_type: str, order_id: int) -> Dict[str, Any]:
        """取消订单"""
        if not self.access_id or not self.secret_key:
            raise ValueError("交易接口需要access_id和secret_key")
        
        path = f"/v2/spot/cancel-order"
        body = {
            "market": market,
            "market_type": market_type,
            "order_id": order_id
        }
        
        return await self._request("POST", path, body=body)
    
    async def get_order_history(self, market_type: str = 'SPOT', market: str = None, side: str = None,
                                page: int = 1, limit: int = 100) -> Dict[str, Any]:
        """获取订单历史"""
        if not self.access_id or not self.secret_key:
            raise ValueError("账户接口需要access_id和secret_key")
        
        path = "/v2/spot/finished-order"
        params = {
            "market_type": market_type,
            "page": page,
            "limit": limit
        }
        if side:
            params["side"] = side
        if market:
            params["market"] = market

        return await self._request("GET", path, params=params)

    async def get_open_orders(self, market: str = None) -> Dict[str, Any]:
        """获取当前挂单"""
        if not self.access_id or not self.secret_key:
            raise ValueError("账户接口需要access_id和secret_key")
        
        path = "/v2/spot/pending-order"
        params = {}
        if market:
            params["market"] = market
        
        return await self._request("GET", path, params)


def validate_environment():
    """验证环境变量配置"""
    access_id = os.getenv('COINEX_ACCESS_ID')
    secret_key = os.getenv('COINEX_SECRET_KEY')
    
    if not access_id or not secret_key:
        print("警告: COINEX_ACCESS_ID 和 COINEX_SECRET_KEY 环境变量未设置")
        print("某些功能（账户信息、交易）将无法使用")
        print("市场数据功能仍可正常使用")
        return False
    
    return True


def get_coinex_client() -> CoinExClient:
    """获取CoinEx客户端实例"""
    return CoinExClient()