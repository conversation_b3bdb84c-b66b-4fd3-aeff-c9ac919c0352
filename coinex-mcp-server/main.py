#!/usr/bin/env python3
"""
CoinEx MCP Server
用于访问CoinEx加密货币交易所的MCP服务器
"""

import sys
import json
from datetime import datetime

from mcp.server.fastmcp import FastMCP
from coinex_client import CoinExClient, validate_environment

# 初始化FastMCP服务器
mcp = FastMCP("coinex-mcp-server")

# 初始化CoinEx客户端
coinex_client = CoinExClient()
has_credentials = validate_environment()

# 是否使用Mock模式 (当没有API凭证时)
USE_MOCK_MODE = not has_credentials

if USE_MOCK_MODE:
    print("警告: 运行在Mock模式下，返回模拟数据", file=sys.stderr)
else:
    print("使用真实CoinEx API", file=sys.stderr)

# Mock数据配置 (备用)
MOCK_SYMBOLS = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "DOGEUSDT"]
MOCK_BASE_PRICES = {
    "BTCUSDT": 45000.0,
    "ETHUSDT": 2800.0, 
    "ADAUSDT": 0.45,
    "DOGEUSDT": 0.08
}

def get_mock_price(symbol: str) -> float:
    """生成模拟价格，基于基础价格加上随机波动"""
    import random
    base_price = MOCK_BASE_PRICES.get(symbol, 100.0)
    fluctuation = random.uniform(-0.05, 0.05)
    return round(base_price * (1 + fluctuation), 6)

def get_mock_24hr_change() -> float:
    """生成模拟的24小时价格变化百分比"""
    import random
    return round(random.uniform(-15.0, 15.0), 2)

def get_mock_volume() -> float:
    """生成模拟交易量"""
    import random
    return round(random.uniform(1000000, ********), 2)

@mcp.tool()
async def get_ticker(symbol: str = None) -> str:
    """获取交易对的价格信息
    
    Args:
        symbol: 交易对符号，如 BTCUSDT。如果不提供则返回所有交易对
    """
    if USE_MOCK_MODE:
        # Mock模式
        if symbol:
            if symbol not in MOCK_SYMBOLS:
                return f"不支持的交易对: {symbol}。支持的交易对: {', '.join(MOCK_SYMBOLS)}"
            
            price = get_mock_price(symbol)
            change_percent = get_mock_24hr_change()
            volume = get_mock_volume()
            
            result = {
                "symbol": symbol,
                "last_price": str(price),
                "change_rate": f"{change_percent}%",
                "volume": str(volume),
                "timestamp": datetime.now().isoformat(),
                "_mode": "mock"
            }
            return json.dumps(result, indent=2, ensure_ascii=False)
        else:
            all_tickers = []
            for sym in MOCK_SYMBOLS:
                price = get_mock_price(sym)
                change_percent = get_mock_24hr_change()
                volume = get_mock_volume()
                
                ticker = {
                    "symbol": sym,
                    "last_price": str(price),
                    "change_rate": f"{change_percent}%",
                    "volume": str(volume)
                }
                all_tickers.append(ticker)
            
            result = {
                "tickers": all_tickers,
                "timestamp": datetime.now().isoformat(),
                "_mode": "mock"
            }
            return json.dumps(result, indent=2, ensure_ascii=False)
    else:
        # 真实API模式
        try:
            if symbol:
                # 转换为CoinEx的market格式 (BTCUSDT)
                market = symbol.upper()
                api_result = await coinex_client.get_ticker(market)
                
                if api_result.get('code') == 0 and 'data' in api_result:
                    data = api_result['data']
                    # CoinEx API返回的是列表，取第一个元素
                    if isinstance(data, list) and data:
                        ticker_data = data[0]
                        # 计算变化率
                        open_price = float(ticker_data.get('open', 0))
                        last_price = float(ticker_data.get('last', 0))
                        change_rate = ((last_price - open_price) / open_price * 100) if open_price > 0 else 0
                        
                        result = {
                            "symbol": symbol,
                            "last_price": str(ticker_data.get('last', '0')),
                            "change_rate": f"{change_rate:.2f}%",
                            "volume": str(ticker_data.get('volume', '0')),
                            "high_24h": str(ticker_data.get('high', '0')),
                            "low_24h": str(ticker_data.get('low', '0')),
                            "open_24h": str(ticker_data.get('open', '0')),
                            "close_24h": str(ticker_data.get('close', '0')),
                            "timestamp": datetime.now().isoformat(),
                            "_mode": "live"
                        }
                        return json.dumps(result, indent=2, ensure_ascii=False)
                    else:
                        return f"获取{symbol}行情失败: 数据格式异常"
                else:
                    return f"获取{symbol}行情失败: {api_result.get('message', '未知错误')}"
            else:
                # 获取所有市场行情
                api_result = await coinex_client.get_ticker()
                
                if api_result.get('code') == 0 and 'data' in api_result:
                    all_tickers = []
                    data = api_result['data']
                    
                    # CoinEx API返回的是列表，不是字典
                    if isinstance(data, list):
                        # 只显示前20个交易对避免输出过多
                        for i, ticker_data in enumerate(data[:20]):
                            # 计算变化率
                            open_price = float(ticker_data.get('open', 0))
                            last_price = float(ticker_data.get('last', 0))
                            change_rate = ((last_price - open_price) / open_price * 100) if open_price > 0 else 0
                            
                            ticker = {
                                "symbol": ticker_data.get('market', 'Unknown'),
                                "last_price": str(ticker_data.get('last', '0')),
                                "change_rate": f"{change_rate:.2f}%",
                                "volume": str(ticker_data.get('volume', '0'))
                            }
                            all_tickers.append(ticker)
                        
                        result = {
                            "tickers": all_tickers,
                            "total_markets": len(data),
                            "showing": len(all_tickers),
                            "timestamp": datetime.now().isoformat(),
                            "_mode": "live"
                        }
                        return json.dumps(result, indent=2, ensure_ascii=False)
                    else:
                        return f"获取市场行情失败: 数据格式异常"
                else:
                    return f"获取市场行情失败: {api_result.get('message', '未知错误')}"
        except Exception as e:
            return f"请求失败: {str(e)}"


@mcp.tool()
async def get_orderbook(symbol: str, limit: int = 20) -> str:
    """获取订单簿信息
    
    Args:
        symbol: 交易对符号，如 BTCUSDT
        limit: 返回的深度数量，默认20
    """
    if USE_MOCK_MODE:
        # Mock模式
        if symbol not in MOCK_SYMBOLS:
            return f"不支持的交易对: {symbol}。支持的交易对: {', '.join(MOCK_SYMBOLS)}"
        
        import random
        current_price = get_mock_price(symbol)
        
        # 生成买单 (bids) - 价格从高到低
        bids = []
        for i in range(limit):
            price = current_price - (i + 1) * (current_price * 0.001)
            quantity = random.uniform(0.1, 10.0)
            bids.append([str(round(price, 6)), str(round(quantity, 4))])
        
        # 生成卖单 (asks) - 价格从低到高  
        asks = []
        for i in range(limit):
            price = current_price + (i + 1) * (current_price * 0.001)
            quantity = random.uniform(0.1, 10.0)
            asks.append([str(round(price, 6)), str(round(quantity, 4))])
        
        result = {
            "symbol": symbol,
            "bids": bids,
            "asks": asks,
            "timestamp": datetime.now().isoformat(),
            "_mode": "mock"
        }
        
        return json.dumps(result, indent=2, ensure_ascii=False)
    else:
        # 真实API模式
        try:
            market = symbol.upper()
            api_result = await coinex_client.get_depth(market, limit, "0")
            
            if api_result.get('code') == 0 and 'data' in api_result:
                data = api_result['data']
                # CoinEx API的订单簿数据在depth字段中
                if 'depth' in data:
                    depth_data = data['depth']
                    result = {
                        "symbol": symbol,
                        "bids": depth_data.get('bids', []),
                        "asks": depth_data.get('asks', []),
                        "last_price": depth_data.get('last', '0'),
                        "timestamp": datetime.now().isoformat(),
                        "_mode": "live"
                    }
                    return json.dumps(result, indent=2, ensure_ascii=False)
                else:
                    return f"获取{symbol}订单簿失败: 数据格式异常"
            else:
                return f"获取{symbol}订单簿失败: {api_result.get('message', '未知错误')}"
        except Exception as e:
            return f"请求失败: {str(e)}"


@mcp.tool()
async def get_kline(symbol: str, period: str = "1hour", limit: int = 100) -> str:
    """获取K线数据
    
    Args:
        symbol: 交易对符号，如 BTCUSDT
        period: 时间周期 (1min, 5min, 15min, 30min, 1hour, 4hour, 1day, 1week)
        limit: 返回数量，默认100
    """
    valid_periods = ["1min", "5min", "15min", "30min", "1hour", "4hour", "1day", "1week"]
    if period not in valid_periods:
        return f"不支持的时间周期: {period}。支持的周期: {', '.join(valid_periods)}"
    
    if USE_MOCK_MODE:
        # Mock模式
        if symbol not in MOCK_SYMBOLS:
            return f"不支持的交易对: {symbol}。支持的交易对: {', '.join(MOCK_SYMBOLS)}"
        
        import random
        base_price = MOCK_BASE_PRICES.get(symbol, 100.0)
        klines = []
        
        # 生成模拟K线数据
        for i in range(limit):
            open_price = base_price * random.uniform(0.95, 1.05)
            close_price = open_price * random.uniform(0.98, 1.02)
            high_price = max(open_price, close_price) * random.uniform(1.0, 1.01)
            low_price = min(open_price, close_price) * random.uniform(0.99, 1.0)
            volume = random.uniform(1000, 100000)
            
            timestamp = int(datetime.now().timestamp() - i * 3600) * 1000
            
            kline = [
                timestamp,
                str(round(open_price, 6)),
                str(round(high_price, 6)),
                str(round(low_price, 6)),
                str(round(close_price, 6)),
                str(round(volume, 2))
            ]
            klines.append(kline)
        
        result = {
            "symbol": symbol,
            "period": period,
            "klines": list(reversed(klines)),
            "timestamp": datetime.now().isoformat(),
            "_mode": "mock"
        }
        
        return json.dumps(result, indent=2, ensure_ascii=False)
    else:
        # 真实API模式
        try:
            market = symbol.upper()
            api_result = await coinex_client.get_klines(market, period, limit)
            
            if api_result.get('code') == 0 and 'data' in api_result:
                data = api_result['data']
                result = {
                    "symbol": symbol,
                    "period": period,
                    "klines": data,
                    "timestamp": datetime.now().isoformat(),
                    "_mode": "live"
                }
                return json.dumps(result, indent=2, ensure_ascii=False)
            else:
                return f"获取{symbol}K线数据失败: {api_result.get('message', '未知错误')}"
        except Exception as e:
            return f"请求失败: {str(e)}"

@mcp.tool()
async def get_account_info() -> str:
    """获取账户信息"""
    if USE_MOCK_MODE:
        # Mock模式
        balances = [
            {"asset": "USDT", "available": "1000.********", "frozen": "100.********"},
            {"asset": "BTC", "available": "0.********", "frozen": "0.********"},
            {"asset": "ETH", "available": "2.********", "frozen": "0.********"},
            {"asset": "ADA", "available": "1000.********", "frozen": "0.********"}
        ]
        
        result = {
            "account_type": "spot",
            "balances": balances,
            "total_balance_usdt": "5678.90",
            "timestamp": datetime.now().isoformat(),
            "_mode": "mock"
        }
        
        return json.dumps(result, indent=2, ensure_ascii=False)
    else:
        # 真实API模式 - 需要认证
        try:
            api_result = await coinex_client.get_balances()
            
            if api_result.get('code') == 0 and 'data' in api_result:
                data = api_result['data']
                result = {
                    "account_type": "spot",
                    "balances": data,
                    "timestamp": datetime.now().isoformat(),
                    "_mode": "live"
                }
                return json.dumps(result, indent=2, ensure_ascii=False)
            else:
                return f"获取账户信息失败: {api_result.get('message', '未知错误')}"
        except ValueError as e:
            return f"错误: {str(e)}"
        except Exception as e:
            return f"请求失败: {str(e)}"

@mcp.tool()
async def place_order(symbol: str, side: str, type: str, quantity: str, price: str = None) -> str:
    """下单交易
    
    Args:
        symbol: 交易对符号
        side: 买卖方向 (buy/sell)
        type: 订单类型 (limit/market)
        quantity: 数量
        price: 价格 (限价单必需)
    """
    if side not in ["buy", "sell"]:
        return "买卖方向必须是 'buy' 或 'sell'"
    
    if type not in ["limit", "market"]:
        return "订单类型必须是 'limit' 或 'market'"
    
    if type == "limit" and not price:
        return "限价单必须提供价格"
    
    if USE_MOCK_MODE:
        # Mock模式
        if symbol not in MOCK_SYMBOLS:
            return f"不支持的交易对: {symbol}。支持的交易对: {', '.join(MOCK_SYMBOLS)}"
        
        import random
        order_id = random.randint(1000000, 9999999)
        result_price = price if price else str(get_mock_price(symbol))
        
        result = {
            "order_id": order_id,
            "symbol": symbol,
            "side": side,
            "type": type,
            "quantity": quantity,
            "price": result_price,
            "status": "submitted",
            "create_time": datetime.now().isoformat(),
            "message": "订单已提交 (Mock模式)",
            "_mode": "mock"
        }
        
        return json.dumps(result, indent=2, ensure_ascii=False)
    else:
        # 真实API模式 - 需要认证
        try:
            market = symbol.upper()
            api_result = await coinex_client.place_order(
                market=market,
                type=type,
                side=side,
                amount=quantity,
                price=price
            )
            
            if api_result.get('code') == 0 and 'data' in api_result:
                data = api_result['data']
                result = {
                    "order_id": data.get('id'),
                    "symbol": symbol,
                    "side": side,
                    "type": type,
                    "quantity": quantity,
                    "price": price or data.get('price', ''),
                    "status": data.get('status', 'submitted'),
                    "create_time": datetime.now().isoformat(),
                    "_mode": "live"
                }
                return json.dumps(result, indent=2, ensure_ascii=False)
            else:
                return f"下单失败: {api_result.get('message', '未知错误')}"
        except ValueError as e:
            return f"错误: {str(e)}"
        except Exception as e:
            return f"请求失败: {str(e)}"

@mcp.tool()
async def cancel_order(symbol: str, order_id: int) -> str:
    """取消订单
    
    Args:
        symbol: 交易对符号
        order_id: 订单ID
    """
    if USE_MOCK_MODE:
        # Mock模式
        if symbol not in MOCK_SYMBOLS:
            return f"不支持的交易对: {symbol}。支持的交易对: {', '.join(MOCK_SYMBOLS)}"
        
        result = {
            "order_id": order_id,
            "symbol": symbol,
            "status": "cancelled",
            "cancel_time": datetime.now().isoformat(),
            "message": "订单已取消 (Mock模式)",
            "_mode": "mock"
        }
        
        return json.dumps(result, indent=2, ensure_ascii=False)
    else:
        # 真实API模式 - 需要认证
        try:
            market = symbol.upper()
            api_result = await coinex_client.cancel_order(market, order_id)
            
            if api_result.get('code') == 0 and 'data' in api_result:
                data = api_result['data']
                result = {
                    "order_id": order_id,
                    "symbol": symbol,
                    "status": "cancelled",
                    "cancel_time": datetime.now().isoformat(),
                    "_mode": "live"
                }
                return json.dumps(result, indent=2, ensure_ascii=False)
            else:
                return f"取消订单失败: {api_result.get('message', '未知错误')}"
        except ValueError as e:
            return f"错误: {str(e)}"
        except Exception as e:
            return f"请求失败: {str(e)}"

@mcp.tool()
async def get_order_history(symbol: str = None, limit: int = 50) -> str:
    """获取订单历史
    
    Args:
        symbol: 交易对符号，不指定则返回所有交易对
        limit: 返回数量限制
    """
    if USE_MOCK_MODE:
        # Mock模式
        import random
        
        symbols_to_use = [symbol] if symbol else MOCK_SYMBOLS[:2]
        
        orders = []
        for sym in symbols_to_use:
            for i in range(min(limit // len(symbols_to_use), 10)):
                order_id = random.randint(1000000, 9999999)
                side = random.choice(["buy", "sell"])
                order_type = random.choice(["limit", "market"])
                status = random.choice(["filled", "cancelled", "partial_filled"])
                
                order = {
                    "order_id": order_id,
                    "symbol": sym,
                    "side": side,
                    "type": order_type,
                    "quantity": str(random.uniform(0.1, 10.0)),
                    "price": str(get_mock_price(sym)),
                    "filled_quantity": str(random.uniform(0, 10.0)),
                    "status": status,
                    "create_time": datetime.now().isoformat()
                }
                orders.append(order)
        
        result = {
            "orders": orders,
            "total_count": len(orders),
            "timestamp": datetime.now().isoformat(),
            "_mode": "mock"
        }
        
        return json.dumps(result, indent=2, ensure_ascii=False)
    else:
        # 真实API模式 - 需要认证
        try:
            market = symbol.upper() if symbol else None
            api_result = await coinex_client.get_order_history(market, limit)
            
            if api_result.get('code') == 0 and 'data' in api_result:
                data = api_result['data']
                result = {
                    "orders": data,
                    "total_count": len(data),
                    "timestamp": datetime.now().isoformat(),
                    "_mode": "live"
                }
                return json.dumps(result, indent=2, ensure_ascii=False)
            else:
                return f"获取订单历史失败: {api_result.get('message', '未知错误')}"
        except ValueError as e:
            return f"错误: {str(e)}"
        except Exception as e:
            return f"请求失败: {str(e)}"

if __name__ == "__main__":
    print("启动CoinEx MCP服务器...", file=sys.stderr)
    mcp.run()